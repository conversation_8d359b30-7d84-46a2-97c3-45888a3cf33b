/**
 * 微信小程序
 * 作者：Tianxx
 * 版本：1.0
 * 日期：2025-07-21
 */
const NOTICE_SWITCH = 1; // 通知开关：1=开启，0=关闭
// 常量配置u  
const APPID = 'wx096c43d1829a7788'; // Nike Plus小程序appid

// 解析命令行参数
const args = process.argv.slice(2);
const getArg = (name) => {
    const index = args.indexOf(`--${name}`);
    return index !== -1 && args[index + 1] ? args[index + 1] : null;
};

// 环境变量和命令行参数
const cmdWxid = getArg('wxid');
const isDebug = args.includes('--debug');
const wxidList = cmdWxid || process.env.TXX_WXID || '';

// 解析wxid列表的函数
function parseWxidList(wxidString) {
    if (!wxidString) return [];

    return wxidString
        .split('\n')
        .map(wxid => wxid.trim())
        .filter(wxid => wxid.length > 0)
        .filter(wxid => !wxid.startsWith('#'));
}

// 引入wxcode模块
const wxcode = require('./wxcode');
const fs = require('fs');
const path = require('path');

// 获取脚本名称（不含扩展名）
const scriptName = path.basename(__filename, '.js');
// Token缓存文件路径
const TOKEN_CACHE_FILE = path.join(__dirname, `${scriptName}_tokens.json`);

class ScriptTemplate {
    constructor(wxid) {
        this.wxid = wxid;
        this.appid = APPID;
        this.isLogin = false;
        this.wxCode = null;
        this.openid = null;
        this.mobileInfo = null;
        this.userProfile = null;
        this.gcToken = null;  // Nike GC Token
        this.authToken = null;  // Nike Auth Token (用于签到接口)
        this.refreshToken = null;  // Nike Refresh Token
        this.upmId = null;  // Nike User ID
        this.cacheExpireTime = null;
    }

    // 读取token缓存
    loadTokenCache() {
        try {
            if (fs.existsSync(TOKEN_CACHE_FILE)) {
                const cacheData = JSON.parse(fs.readFileSync(TOKEN_CACHE_FILE, 'utf8'));
                const userCache = cacheData[this.wxid];

                if (userCache && userCache.cacheExpireTime > Date.now()) {
                    this.wxCode = userCache.wxCode;
                    this.openid = userCache.openid;
                    this.mobileInfo = userCache.mobileInfo;
                    this.userProfile = userCache.userProfile;
                    this.cacheExpireTime = userCache.cacheExpireTime;
                    this.isLogin = true;

                    if (isDebug) {
                        console.log(`[DEBUG] 从缓存加载数据成功`);
                        console.log(`[DEBUG] 微信Code: ${this.wxCode}`);
                        console.log(`[DEBUG] OpenID: ${this.openid}`);
                        console.log(`[DEBUG] 缓存过期时间: ${new Date(this.cacheExpireTime).toLocaleString()}`);
                    }
                    return true;
                } else if (userCache) {
                    if (isDebug) console.log(`[DEBUG] 缓存数据已过期`);
                }
            }
        } catch (error) {
            if (isDebug) console.log(`[DEBUG] 读取缓存失败: ${error.message}`);
        }
        return false;
    }

    // 保存数据到缓存
    saveTokenCache() {
        try {
            let cacheData = {};

            // 读取现有缓存
            if (fs.existsSync(TOKEN_CACHE_FILE)) {
                try {
                    cacheData = JSON.parse(fs.readFileSync(TOKEN_CACHE_FILE, 'utf8'));
                } catch (e) {
                    if (isDebug) console.log(`[DEBUG] 现有缓存文件格式错误，将重新创建`);
                }
            }

            // 设置缓存过期时间（默认2小时）
            const expireTime = Date.now() + (2 * 60 * 60 * 1000);

            // 更新当前用户的缓存信息
            cacheData[this.wxid] = {
                wxCode: this.wxCode,
                openid: this.openid,
                mobileInfo: this.mobileInfo,
                userProfile: this.userProfile,
                cacheExpireTime: expireTime,
                updateTime: Date.now()
            };

            this.cacheExpireTime = expireTime;

            // 写入文件
            fs.writeFileSync(TOKEN_CACHE_FILE, JSON.stringify(cacheData, null, 2), 'utf8');

            if (isDebug) {
                console.log(`[DEBUG] 缓存保存成功`);
                console.log(`[DEBUG] 缓存文件: ${TOKEN_CACHE_FILE}`);
                console.log(`[DEBUG] 过期时间: ${new Date(expireTime).toLocaleString()}`);
            }
        } catch (error) {
            console.log(`❌ 保存缓存失败: ${error.message}`);
        }
    }

    // 获取微信授权码并登录
    async getWxCodeAndLogin() {
        if (isDebug) console.log(`[DEBUG] 开始获取微信授权码...`);

        const codeResult = await wxcode.getWxCode(this.wxid, this.appid);
        if (!codeResult.success) {
            console.log(`获取授权码失败：${codeResult.error}`);
            return false;
        }

        this.wxCode = codeResult.code;
        if (isDebug) console.log(`[DEBUG] 获取授权码成功：${this.wxCode}`);

        this.isLogin = true;
        return true;
    }

    // 获取用户openid
    async getUserOpenid() {
        const result = await wxcode.getOpenid(this.wxid, this.appid);
        if (result.success) {
            this.openid = result.openid;
            if (isDebug) console.log(`[DEBUG] 获取openid成功：${this.openid}`);
            return this.openid;
        } else {
            console.log(`获取openid失败：${result.error}`);
            return null;
        }
    }

    // 获取手机号
    async getMobileInfo() {
        const result = await wxcode.getmobile(this.wxid, this.appid);
        if (result.success) {
            this.mobileInfo = result;
            if (isDebug) console.log(`[DEBUG] 获取手机号加密数据成功`);
            return this.mobileInfo;
        } else {
            console.log(`获取手机号失败：${result.error}`);
            return null;
        }
    }

    // 获取用户个人信息（云函数调用）
    async getUserProfile() {
        const cloudFunctionData = JSON.stringify({
            "api_name": "webapi_getuserprofile",
            "data": {
                "app_version": 68,
                "desc": "用于获取您的个人信息",
                "lang": "en",
                "version": "3.7.12"
            },
            "env": 1,
            "operate_directly": false,
            "show_confirm": true,
            "tid": Date.now() * 1000000 + Math.floor(Math.random() * 1000000), // 生成唯一tid
            "with_credentials": true
        });

        const result = await wxcode.getUserInfo(this.wxid, this.appid, cloudFunctionData);
        if (result.success) {
            if (isDebug) console.log(`[DEBUG] 获取用户个人信息成功`);
            // 解析用户信息
            try {
                const userInfo = JSON.parse(result.rawData.data);
                if (isDebug) {
                    console.log(`[DEBUG] 用户信息:`, {
                        nickName: userInfo.nickName,
                        gender: userInfo.gender,
                        avatarUrl: userInfo.avatarUrl,
                        city: userInfo.city,
                        province: userInfo.province,
                        country: userInfo.country
                    });
                }
                this.userProfile = {
                    success: true,
                    userInfo: userInfo,
                    signature: result.signature,
                    encryptedData: result.encryptedData,
                    iv: result.iv
                };
                return this.userProfile;
            } catch (e) {
                console.log(`解析用户信息失败：${e.message}`);
                return { success: false, error: e.message };
            }
        } else {
            console.log(`获取用户个人信息失败：${result.error}`);
            return null;
        }
    }

    // 验证缓存数据是否仍然有效
    async validateCache() {
        if (!this.isLogin || !this.wxCode) return false;

        if (isDebug) console.log(`[DEBUG] 验证缓存数据有效性...`);

        try {
            // 尝试获取一个简单的信息来验证登录状态
            const testResult = await wxcode.getOpenid(this.wxid, this.appid);
            if (testResult.success) {
                if (isDebug) console.log(`[DEBUG] 缓存数据验证通过`);
                return true;
            }
        } catch (error) {
            if (isDebug) console.log(`[DEBUG] 缓存数据验证失败: ${error.message}`);
        }

        if (isDebug) console.log(`[DEBUG] 缓存数据已失效`);
        this.isLogin = false;
        return false;
    }

    // 执行完整的数据获取流程
    async performFullLogin() {
        if (isDebug) console.log(`[DEBUG] 执行完整的数据获取流程...`);

        // 1. 获取授权码并登录
        const loginSuccess = await this.getWxCodeAndLogin();
        if (!loginSuccess) {
            console.log(`[${this.wxid}] 获取授权码失败，跳过`);
            return false;
        }

        // 2. 根据需要获取其他数据（这里可以根据业务需求调整）
        // await this.getUserOpenid();     // 获取openid，取消此行注释
        // await this.getMobileInfo();     // 获取手机号，取消此行注释
        // await this.getUserProfile();    // 获取云函数用户个人信息，取消此行注释

        // 3. 保存到缓存
        this.saveTokenCache();

        return true;
    }

    // 主要业务逻辑
    async run() {
        try {
            // 1. 尝试从缓存加载数据
            const cacheLoaded = this.loadTokenCache();

            if (cacheLoaded) {
                console.log(`📦 使用缓存的数据`);

                // 验证缓存数据是否仍然有效
                const cacheValid = await this.validateCache();
                if (!cacheValid) {
                    console.log(`⚠️ 缓存的数据已失效，重新获取...`);
                    const fullLoginSuccess = await this.performFullLogin();
                    if (!fullLoginSuccess) {
                        console.log(`[${this.wxid}] 完整登录失败，跳过`);
                        return;
                    }
                } else {
                    console.log(`✅ 缓存的数据有效`);
                }
            } else {
                // 2. 缓存无效或不存在，进行完整登录
                const fullLoginSuccess = await this.performFullLogin();
                if (!fullLoginSuccess) {
                    print(`[${this.wxid}] 完整登录失败，跳过`, true);
                    return;
                }
            }

            // 3. 在这里添加你的具体业务逻辑
            // 现在可以使用 this.wxCode, this.openid, this.mobileInfo, this.userProfile 等数据

            // Nike小程序API测试
            if (this.wxCode) {
                console.log(`[${this.wxid}] 🧪 开始测试Nike API...`);
                try {
                    // 基于源码分析的正确认证流程
                    console.log(`[${this.wxid}] 🔑 开始Nike完整认证流程...`);

                    // 1. 重新获取新的微信code (因为code只能使用一次)
                    console.log(`[${this.wxid}] 🔄 重新获取微信授权码...`);
                    const newCodeResult = await wxcode.getWxCode(this.wxid, this.appid);
                    if (newCodeResult.success) {
                        this.wxCode = newCodeResult.code;
                        console.log(`[${this.wxid}] ✅ 新授权码获取成功`);
                    } else {
                        console.log(`[${this.wxid}] ❌ 获取新授权码失败: ${newCodeResult.error}`);
                        return;
                    }

                    // 2. 获取GC Token (用于一般业务接口)
                    await this.getGCToken();

                    // 3. 获取Auth Token (用于签到等特殊接口)
                    await this.getAuthToken();
                    console.log(`[${this.wxid}] ✅ Nike认证流程完成`);

                    // 尝试使用不同的Token执行Nike签到任务
                    if (this.authToken) {
                        console.log(`[${this.wxid}] 🎯 使用Auth Token执行Nike签到任务...`);
                        await this.performNikeCheckIn(this.authToken);
                    } else if (this.gcToken) {
                        console.log(`[${this.wxid}] 🎯 尝试使用GC Token执行Nike签到任务...`);
                        await this.performNikeCheckIn(this.gcToken);
                    } else {
                        console.log(`[${this.wxid}] ⚠️ 未获取到任何Token，跳过签到任务`);
                    }
                } catch (apiError) {
                    console.log(`[${this.wxid}] ❌ Nike API测试失败: ${apiError.message}`);
                }
            }
        } catch (error) {
            console.log(`[${this.wxid}] 脚本执行出错：${error.message}`);
            if (isDebug) {
                console.error(error);
            }
        }
    }

    // Nike签到相关方法
    async performNikeCheckIn(token) {
        console.log(`[${this.wxid}] 🎯 开始Nike签到流程...`);

        try {
            // 1. 获取签到中心信息 (包含任务列表和taskId)
            const centerInfo = await this.getRedeemCenterInfo(token);

            // 2. 获取用户当前积分
            const userPoints = await this.getUserPoints(token);

            // 3. 从签到中心信息中提取签到任务ID
            let signTaskId = null;
            if (centerInfo && centerInfo.tasks) {
                const signTask = centerInfo.tasks.find(task =>
                    task.category === 'SIGN' ||
                    task.type === 'DAILY' ||
                    task.name?.includes('签到') ||
                    task.name?.includes('sign')
                );
                if (signTask) {
                    signTaskId = signTask.id || signTask.taskId;
                    console.log(`[${this.wxid}] 🎯 找到签到任务ID: ${signTaskId}`);
                }
            }

            // 4. 执行每日签到 (使用正确的taskId)
            await this.completeDailySign(token, signTaskId);

            // 5. 获取积分记录
            await this.getPointsLog(token);

            console.log(`[${this.wxid}] ✅ Nike签到流程完成`);
            console.log(`[${this.wxid}] 📊 当前积分: ${userPoints || '未知'}`);

        } catch (error) {
            console.log(`[${this.wxid}] ❌ Nike签到流程失败: ${error.message}`);
            if (isDebug) {
                console.error(error);
            }
        }
    }

    // 获取签到中心信息 (使用GC Token)
    async getRedeemCenterInfo(gcToken) {
        console.log(`[${this.wxid}] 📊 获取签到中心信息...`);

        try {
            const response = await wxcode.httpRequest('GET',
                'https://wechat.nike.com.cn/onemp/redeem/redeem_center_info/v2',
                null, {
                    'Authorization': `Bearer ${gcToken}`,  // 使用GC Token
                    'Accept': 'application/json'
                }
            );

            if (response && response.code === 0) {
                console.log(`[${this.wxid}] ✅ 签到中心信息获取成功`);
                if (isDebug) {
                    console.log(`[${this.wxid}] 签到中心信息:`, JSON.stringify(response.data, null, 2));
                }
                return response.data;
            } else {
                console.log(`[${this.wxid}] ⚠️ 签到中心信息响应异常:`, response);
            }
        } catch (error) {
            console.log(`[${this.wxid}] ❌ 获取签到中心信息失败: ${error.message}`);
        }
    }

    // 获取用户积分 (使用GC Token)
    async getUserPoints(gcToken) {
        console.log(`[${this.wxid}] 💰 获取用户积分...`);

        try {
            const response = await wxcode.httpRequest('GET',
                'https://wechat.nike.com.cn/onemp/redeem/user_points_at_time/v2',
                null, {
                    'Authorization': `Bearer ${gcToken}`,  // 使用GC Token
                    'Accept': 'application/json'
                }
            );

            if (response && response.code === 0) {
                const points = response.data?.points || 0;
                console.log(`[${this.wxid}] ✅ 当前积分: ${points}`);
                return points;
            } else {
                console.log(`[${this.wxid}] ⚠️ 获取积分响应异常:`, response);
            }
        } catch (error) {
            console.log(`[${this.wxid}] ❌ 获取用户积分失败: ${error.message}`);
        }
    }

    // 完成每日签到 (使用正确的taskId)
    async completeDailySign(token, taskId = null) {
        console.log(`[${this.wxid}] ✍️ 执行每日签到...`);

        try {
            // 如果有从签到中心获取的taskId，优先使用
            let signTaskIds = [];
            if (taskId) {
                signTaskIds.push(taskId);
                console.log(`[${this.wxid}] 🎯 使用从签到中心获取的任务ID: ${taskId}`);
            }

            // 添加备用的签到任务ID
            signTaskIds.push(...['daily_sign', 'daily_check_in', '1', 'sign', 'SIGN']);

            for (const currentTaskId of signTaskIds) {
                try {
                    console.log(`[${this.wxid}] 尝试签到任务ID: ${currentTaskId}`);

                    const response = await wxcode.httpRequest('GET',
                        `https://wechat.nike.com.cn/onemp/redeem/complete_daily_sign/v2/${currentTaskId}`,
                        null, {
                            'Authorization': `Bearer ${token}`,  // 使用GC Token作为Authorization
                            'Accept': 'application/json'
                        }
                    );

                    if (response && response.code === 0) {
                        console.log(`[${this.wxid}] ✅ 每日签到成功！任务ID: ${currentTaskId}`);
                        if (response.data?.reward) {
                            console.log(`[${this.wxid}] 🎁 签到奖励: ${JSON.stringify(response.data.reward)}`);
                        }
                        if (response.data?.points) {
                            console.log(`[${this.wxid}] 💰 获得积分: ${response.data.points}`);
                        }
                        return response.data;
                    } else if (response && response.code === 1001) {
                        console.log(`[${this.wxid}] ℹ️ 今日已签到 (任务ID: ${currentTaskId})`);
                        return response.data;
                    } else if (response && response.statusCode !== 404) {
                        console.log(`[${this.wxid}] ⚠️ 签到响应异常 (任务ID: ${currentTaskId}):`, response);
                    }
                } catch (taskError) {
                    console.log(`[${this.wxid}] ❌ 签到任务ID ${currentTaskId} 失败: ${taskError.message}`);
                }
            }

            console.log(`[${this.wxid}] ⚠️ 所有签到任务ID都失败了`);

        } catch (error) {
            console.log(`[${this.wxid}] ❌ 每日签到失败: ${error.message}`);
        }
    }

    // 获取积分记录 (使用GC Token和正确的版本)
    async getPointsLog(gcToken) {
        console.log(`[${this.wxid}] 📋 获取积分记录...`);

        try {
            const response = await wxcode.httpRequest('GET',
                'https://wechat.nike.com.cn/onemp/redeem/points_log/v1',
                null, {
                    'Authorization': `Bearer ${gcToken}`,  // 使用GC Token
                    'Accept': 'application/json'
                }
            );

            if (response && response.code === 0) {
                console.log(`[${this.wxid}] ✅ 积分记录获取成功`);
                if (isDebug && response.data?.logs) {
                    console.log(`[${this.wxid}] 最近积分记录:`, JSON.stringify(response.data.logs.slice(0, 3), null, 2));
                }
                return response.data;
            } else {
                console.log(`[${this.wxid}] ⚠️ 积分记录响应异常:`, response);
            }
        } catch (error) {
            console.log(`[${this.wxid}] ❌ 获取积分记录失败: ${error.message}`);
        }
    }

    // 获取GC Token (用于一般业务接口)
    async getGCToken() {
        console.log(`[${this.wxid}] 🔑 获取Nike GC Token...`);

        try {
            const loginData = {
                appId: `wechat:mp:${APPID}`,
                code: this.wxCode
            };

            const response = await wxcode.httpRequest('POST',
                'https://wechat.nike.com.cn/wechat_auth/token/v1',
                loginData, {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            );

            if (response && (response.accessToken || (response.data && response.data.accessToken))) {
                this.gcToken = response.accessToken || response.data.accessToken;
                console.log(`[${this.wxid}] ✅ GC Token获取成功`);
                if (isDebug) {
                    console.log(`[${this.wxid}] GC Token:`, this.gcToken.substring(0, 50) + '...');
                }
            } else {
                console.log(`[${this.wxid}] ⚠️ GC Token响应异常:`, response);
            }
        } catch (error) {
            console.log(`[${this.wxid}] ❌ 获取GC Token失败: ${error.message}`);
        }
    }

    // 获取Auth Token (用于签到接口) - 基于源码中的silentLogin流程
    async getAuthToken() {
        console.log(`[${this.wxid}] 🔑 获取Nike Auth Token...`);

        try {
            const loginData = {
                appId: APPID,  // 直接使用appId，不加前缀
                code: this.wxCode
            };

            // 使用源码中发现的关键接口，添加正确的请求头
            const response = await wxcode.httpRequest('POST',
                'https://accounts.nike.com.cn/bind_user_tokens/wechat_mp/v1',
                loginData, {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'HOST': 'accounts.nike.com.cn'  // 添加HOST头
                }
            );

            if (response && response.statusCode === 200 && response.data) {
                // 格式化认证响应 (基于源码中的formatAuthRes逻辑)
                const authData = this.formatAuthResponse(response.data);

                this.authToken = authData.accessToken;
                this.refreshToken = authData.refreshToken;
                this.upmId = authData.upmId;

                console.log(`[${this.wxid}] ✅ Auth Token获取成功`);
                if (isDebug) {
                    console.log(`[${this.wxid}] Auth Token:`, this.authToken.substring(0, 50) + '...');
                    console.log(`[${this.wxid}] UPM ID:`, this.upmId);
                }
            } else {
                console.log(`[${this.wxid}] ⚠️ Auth Token响应异常:`, response);
            }
        } catch (error) {
            console.log(`[${this.wxid}] ❌ 获取Auth Token失败: ${error.message}`);
        }
    }

    // 格式化认证响应 (基于源码中的formatAuthRes函数)
    formatAuthResponse(data) {
        try {
            // 解析JWT token获取用户信息
            let userInfo = null;
            if (data.id_token) {
                const payload = JSON.parse(Buffer.from(data.id_token.split('.')[1], 'base64').toString());
                userInfo = payload;
            } else if (data.user) {
                userInfo = data.user;
            }

            const upmId = userInfo && userInfo.sub ? userInfo.sub : '';
            const expiresIn = Number(data.expires_in || data.expiresIn || 3600);
            const accessToken = data.access_token || data.accessToken;
            const refreshToken = data.refresh_token || data.refreshToken;

            return {
                upmId: upmId,
                accessToken: accessToken,
                refreshToken: refreshToken,
                expiresIn: expiresIn,
                swoosh: Boolean(userInfo && userInfo.swoosh)
            };
        } catch (error) {
            console.log(`[${this.wxid}] ❌ 格式化认证响应失败: ${error.message}`);
            return {
                upmId: '',
                accessToken: data.access_token || data.accessToken,
                refreshToken: data.refresh_token || data.refreshToken,
                expiresIn: Number(data.expires_in || data.expiresIn || 3600)
            };
        }
    }
}

// 主函数
async function main() {
    console.log(`🔔 脚本开始执行`);

    if (isDebug) {
        console.log(`[DEBUG] 调试模式已开启`);
        console.log(`[DEBUG] APPID: ${APPID}`);
    }

    if (!wxidList) {
        console.log(`❌ 未设置环境变量 TXX_WXID 或命令行参数 --wxid`);
        return;
    }

    // 处理单个wxid或多个wxid
    const wxids = cmdWxid ? [cmdWxid] : parseWxidList(wxidList);

    if (wxids.length === 0) {
        console.log(`❌ 没有找到有效的wxid`);
        return;
    }

    console.log(`📋 共找到 ${wxids.length} 个有效账号`);

    if (isDebug) {
        console.log(`[DEBUG] 账号列表: ${wxids.join(', ')}`);
    }

    // 逐个处理账号
    for (let i = 0; i < wxids.length; i++) {
        const wxid = wxids[i];
        console.log(`\n🚀 [${i + 1}/${wxids.length}] 开始处理账号: ${wxid}`);

        try {
            const script = new ScriptTemplate(wxid);
            await script.run();
            console.log(`✅ [${i + 1}/${wxids.length}] 账号 ${wxid} 处理完成`);
        } catch (error) {
            console.log(`❌ [${i + 1}/${wxids.length}] 账号 ${wxid} 处理失败: ${error.message}`);
            if (isDebug) {
                console.error(error);
            }
        }

        console.log('─'.repeat(60));

        // 如果不是最后一个账号，稍微延迟一下
        if (i < wxids.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    }

    console.log(`\n🎉 所有账号处理完成！`);

    // 发送通知
    if (NOTICE_SWITCH && notice) {
        await sendMsg(notice);
    }
}

// 通知相关变量和函数
let notice = '';

function print(msg, is_notice = false) {
    let str = `${msg}`;
    console.log(str);
    if (NOTICE_SWITCH && is_notice) {
        notice += `${str}\n`;
    }
}

async function sendMsg(message) {
    try {
        let notify = '';
        try {
            notify = require('./sendNotify');
        } catch (e) {
            try {
                notify = require("../sendNotify");
            } catch (e2) {
                console.log('❌ 未找到sendNotify模块，无法发送通知');
                return;
            }
        }
        await notify.sendNotify(scriptName, message);
        console.log('📢 通知发送成功');
    } catch (error) {
        console.log(`❌ 通知发送失败: ${error.message}`);
    }
}

// 执行脚本
main().catch(console.error);
