# Nike小程序登录测试工具

## 概述

这是一个用于测试Nike小程序完整登录流程的工具，整合了Nike小程序的所有登录相关接口和认证流程。

## 功能特性

### 🔐 完整登录流程
- **微信小程序授权**: 获取微信临时授权码
- **Nike微信API登录**: 通过Nike的微信认证接口获取token
- **OpenID/UnionID获取**: 获取用户的微信身份标识
- **静默登录**: Nike账户系统的静默登录机制
- **OAuth2.0认证**: 标准的OAuth2.0令牌获取流程

### 🧪 API接口测试
- **菜单分类API**: 测试Nike商品分类接口
- **用户信息API**: 获取用户详细资料
- **手机号获取**: 获取用户手机号信息
- **已认证API调用**: 测试需要登录的业务接口

### 📊 测试报告
- 详细的登录流程日志
- 自动生成JSON格式测试报告
- 登录状态和获取信息的汇总

## 安装依赖

```bash
npm install request crypto
```

## 使用方法

### 基本使用

```bash
# 使用命令行参数指定微信ID
node nike-login-test.js --wxid your_wxid_here

# 开启详细调试日志
node nike-login-test.js --wxid your_wxid_here --debug

# 使用环境变量
TEST_WXID=your_wxid_here node nike-login-test.js
```

### 查看帮助

```bash
node nike-login-test.js --help
```

## 配置说明

### Nike小程序配置

工具中已预配置Nike小程序的相关信息：

```javascript
const NIKE_CONFIG = {
    appId: 'wx096c43d1829a7788',           // Nike Plus小程序ID
    accounts: {
        url: 'https://accounts.nike.com.cn',
        clientId: 'b75a7f09c6f0a046a73b97532ac971b',
        redirectUri: 'https://mp-static-assets.gc.nike.com/auth/wechat-shop/index.html'
    },
    nikeApiBaseurl: 'https://api.nike.com.cn',
    wechatApi: 'https://wechat.nike.com.cn'
};
```

### 环境变量

- `TEST_WXID`: 测试用的微信ID
- `NIKE_LOGS`: 设置为'1'开启详细日志

## 登录流程详解

### 1. 微信授权码获取
```
wx.login() -> 获取临时授权码
```

### 2. Nike微信API登录
```
POST /wechat_auth/token/v1
{
  "appId": "wechat:mp:wx096c43d1829a7788",
  "code": "微信授权码"
}
```

### 3. OpenID/UnionID获取
```
POST /wechat_auth/weChatMiniOpenIdExchange/v1
{
  "appId": "wechat:mp:wx096c43d1829a7788",
  "code": "微信授权码"
}
```

### 4. 静默登录Token
```
POST /bind_user_tokens/wechat_mp/v1
{
  "appId": "wx096c43d1829a7788",
  "code": "微信授权码"
}
```

### 5. OAuth2.0 Token获取
```
POST /token/v1
{
  "grant_type": "authorization_code",
  "client_id": "b75a7f09c6f0a046a73b97532ac971b",
  "code": "授权码",
  "code_verifier": "PKCE验证码"
}
```

## API接口测试

工具会自动测试以下Nike API接口：

1. **菜单分类**: `GET /onemp/menu_categories/v1`
2. **子菜单分类**: `POST /onemp/sub_menu_categories/v1`
3. **分类分享**: `GET /onemp/category/sharing/v1`

所有API调用都会携带正确的认证头：
```http
Authorization: Bearer <token>
App-Id: wechat:mp:wx096c43d1829a7788
```

## 输出示例

```
🔔 Nike小程序登录测试开始
==================================================
📱 测试账号: test_wxid_123
🔧 调试模式: 开启
⏰ 开始时间: 2025-08-01 11:36:05

🚀 开始Nike小程序完整登录流程 - 账号: test_wxid_123
============================================================

🔐 步骤1: 获取微信授权码...
✅ 微信授权码获取成功: mock_code_1754019365

🔐 步骤2: Nike微信API登录...
✅ Nike微信API登录成功

🔐 步骤3: 获取OpenID和UnionID...
✅ OpenID: ogvnS5OowDv5iZH-LYu98wGDWLLk
✅ UnionID: oJ70cuEPftXuiC38psQ8xrJOO6KI

🔐 步骤6: 测试已认证的API调用...
🧪 测试API: 菜单分类
✅ 菜单分类 API调用成功

🎉 Nike小程序登录流程完成！
============================================================
📊 登录结果汇总:
👤 用户信息: {"openId":"ogvnS5OowDv5iZH-LYu98wGDWLLk","unionId":"oJ70cuEPftXuiC38psQ8xrJOO6KI"}
🔑 令牌类型: ["wechatToken"]

💾 测试报告已保存到: nike-login-report-1754019365.json

✅ 测试完成！
⏰ 结束时间: 2025-08-01 11:36:10
```

## 依赖模块

### wxcode.js
工具会尝试使用`wxcode.js`模块获取真实的微信授权码。如果模块不可用，会使用模拟数据进行测试。

确保`wxcode.js`模块包含以下方法：
- `getWxCode(wxid, appid)`: 获取微信授权码
- `getUserInfo(wxid, appid, data)`: 获取用户信息
- `getmobile(wxid, appid)`: 获取手机号信息

## 注意事项

1. **真实环境**: 某些API需要真实的Nike账户和有效的微信授权码才能正常工作
2. **网络环境**: 确保能够访问Nike的API服务器
3. **频率限制**: 注意API调用频率，避免被限制
4. **数据安全**: 测试报告可能包含敏感信息，请妥善保管

## 故障排除

### 常见问题

1. **获取授权码失败**
   - 检查wxcode.js模块是否正确配置
   - 确认微信ID是否有效

2. **API调用失败**
   - 检查网络连接
   - 确认API地址是否正确
   - 查看详细错误日志

3. **认证失败**
   - 检查小程序ID和配置是否正确
   - 确认token是否有效

### 调试模式

使用`--debug`参数开启详细日志，查看完整的请求和响应信息：

```bash
node nike-login-test.js --wxid your_wxid --debug
```

## 文件说明

- `nike-login-test.js`: 主要测试脚本
- `wxcode.js`: 微信授权码获取模块（需要单独配置）
- `nike-login-report-*.json`: 自动生成的测试报告
- `README-Nike-Login.md`: 本说明文档

## 更新日志

### v2.0.0
- 完整的Nike小程序登录流程实现
- 多个API接口测试
- 自动生成测试报告
- 详细的调试日志
- 命令行参数支持
