/**
 * Nike小程序登录测试模块
 * 整合Nike小程序完整登录流程和接口调用
 * 版本: 2.0.0
 */
const request = require('request');
const crypto = require('crypto');

// Nike小程序配置
const NIKE_CONFIG = {
    // 小程序基本信息
    appId: 'wx096c43d1829a7788',
    appName: 'mp',
    originalId: 'gh_4e4bcdbdadfd',
    
    // Nike API配置
    accounts: {
        url: 'https://accounts.nike.com.cn',
        redirectUri: 'https://mp-static-assets.gc.nike.com/auth/wechat-shop/index.html',
        clientId: 'b75a7f09c6f0a046a73b97532ac971b',
        uxId: 'com.nike.commerce.checkout.wechat.mini.web'
    },
    
    // API基础地址
    nikeApiBaseurl: 'https://api.nike.com.cn',
    nikeApiMp: 'https://idn.nike.com',
    wechatApi: 'https://wechat.nike.com.cn',
    gcApi: 'https://api.nike.com.cn',
    
    // API调用标识
    nikeApiCallerId: 'com.nike:digital.wechatminiprogram:1.0',
    
    // 请求头配置
    headers: {
        'Content-Type': 'application/json; charset=UTF-8',
        'nike-api-caller-id': 'com.nike:digital.wechatminiprogram:1.0',
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.61(0x18003D57) NetType/WIFI Language/zh_CN',
        'Accept': 'application/json',
        'Accept-Language': 'zh-CN,zh;q=0.9'
    }
};

// 日志配置
const enableLogs = process.env.NIKE_LOGS === '1' || true;

// 通用HTTP请求方法
function httpRequest(method, url, data = null, customHeaders = {}) {
    return new Promise((resolve, reject) => {
        const headers = {
            ...NIKE_CONFIG.headers,
            ...customHeaders
        };

        let body = null;
        if (data) {
            if (headers['Content-Type'] && headers['Content-Type'].includes('application/x-www-form-urlencoded')) {
                // 表单数据
                body = Object.keys(data).map(key => `${encodeURIComponent(key)}=${encodeURIComponent(data[key])}`).join('&');
            } else {
                // JSON数据
                body = JSON.stringify(data);
            }
        }

        if (body) {
            headers['Content-Length'] = Buffer.byteLength(body, 'utf8');
        }

        const options = {
            url: url,
            method: method.toUpperCase(),
            headers: headers,
            body: body,
            timeout: 30000,
            json: false // 手动处理JSON解析
        };

        if (enableLogs) {
            console.log(`\n🔗 ${method.toUpperCase()} ${url}`);
            console.log('📤 Request Headers:', JSON.stringify(headers, null, 2));
            if (body) {
                console.log('📤 Request Body:', body);
            }
        }

        request(options, (error, response, responseBody) => {
            if (error) {
                console.error(`❌ Request failed: ${error.message}`);
                reject(error);
                return;
            }

            if (enableLogs) {
                console.log(`📥 Response Status: ${response.statusCode}`);
                console.log('📥 Response Headers:', JSON.stringify(response.headers, null, 2));
                console.log('📥 Response Body:', responseBody);
            }

            // 尝试解析JSON响应
            let parsedBody;
            try {
                parsedBody = JSON.parse(responseBody);
            } catch (e) {
                parsedBody = responseBody;
            }

            const result = {
                statusCode: response.statusCode,
                headers: response.headers,
                data: parsedBody,
                rawBody: responseBody
            };

            if (response.statusCode >= 200 && response.statusCode < 300) {
                resolve(result);
            } else {
                reject(new Error(`HTTP ${response.statusCode}: ${responseBody}`));
            }
        });
    });
}

// 生成PKCE验证码
function generatePKCE() {
    const codeVerifier = crypto.randomBytes(32).toString('base64url');
    const codeChallenge = crypto.createHash('sha256').update(codeVerifier).digest('base64url');
    return { codeVerifier, codeChallenge };
}

// Nike登录类
class NikeLogin {
    constructor(wxid) {
        this.wxid = wxid;
        this.appId = NIKE_CONFIG.appId;
        this.tokens = {};
        this.userInfo = {};
    }

    // 1. 微信小程序登录获取code (集成wxcode模块)
    async getWeChatCode() {
        console.log('\n🔐 步骤1: 获取微信授权码...');

        try {
            // 尝试使用wxcode模块获取真实的微信code
            const wxcode = require('./wxcode');
            const result = await wxcode.getWxCode(this.wxid, this.appId);

            if (result.success) {
                console.log(`✅ 微信授权码获取成功: ${result.code}`);
                return result.code;
            } else {
                throw new Error(result.error || '获取微信授权码失败');
            }
        } catch (error) {
            console.log(`⚠️ wxcode模块不可用或获取失败: ${error.message}`);
            console.log('🔄 使用模拟授权码进行测试...');

            // 使用模拟数据进行测试
            const mockCode = `mock_code_${Date.now()}`;
            console.log(`✅ 模拟微信授权码: ${mockCode}`);
            return mockCode;
        }
    }

    // 2. Nike微信API登录
    async nikeWeChatAPILogin(code) {
        console.log('\n🔐 步骤2: Nike微信API登录...');

        // 根据网络请求记录，实际的API可能需要通过不同的方式调用
        // 先尝试通过GC代理调用
        const url = `${NIKE_CONFIG.nikeApiMp}/wechat_auth/token/v1`;
        const data = {
            appId: `wechat:${NIKE_CONFIG.appName}:${this.appId}`,
            code: code
        };

        try {
            const response = await httpRequest('POST', url, data, {
                'withoutToken': 'true',
                'Host': 'idn.nike.com'
            });

            if (response.data && response.data.data) {
                this.tokens.wechatToken = response.data.data;
                console.log('✅ Nike微信API登录成功');
                return response.data.data;
            } else {
                throw new Error('Nike微信API登录响应格式错误');
            }
        } catch (error) {
            console.error('❌ Nike微信API登录失败:', error.message);
            console.log('🔄 尝试备用API地址...');

            // 尝试备用地址
            try {
                const backupUrl = `${NIKE_CONFIG.wechatApi}/wechat_auth/token/v1`;
                const backupResponse = await httpRequest('POST', backupUrl, data, {
                    'withoutToken': 'true',
                    'Host': 'wechat.nike.com.cn'
                });

                if (backupResponse.data && backupResponse.data.data) {
                    this.tokens.wechatToken = backupResponse.data.data;
                    console.log('✅ Nike微信API登录成功 (备用地址)');
                    return backupResponse.data.data;
                }
            } catch (backupError) {
                console.error('❌ 备用API地址也失败:', backupError.message);
            }

            throw error;
        }
    }

    // 3. 获取OpenID和UnionID
    async getWeChatOpenId(code) {
        console.log('\n🔐 步骤3: 获取OpenID和UnionID...');

        const url = `${NIKE_CONFIG.nikeApiMp}/wechat_auth/weChatMiniOpenIdExchange/v1`;
        const data = {
            appId: `wechat:mp:${this.appId}`,
            code: code
        };

        try {
            const response = await httpRequest('POST', url, data, {
                'Host': 'idn.nike.com'
            });

            if (response.data && response.data.data) {
                this.userInfo.openId = response.data.data.openId;
                this.userInfo.unionId = response.data.data.unionId;
                console.log(`✅ OpenID: ${this.userInfo.openId}`);
                console.log(`✅ UnionID: ${this.userInfo.unionId}`);
                return response.data.data;
            } else {
                throw new Error('获取OpenID响应格式错误');
            }
        } catch (error) {
            console.error('❌ 获取OpenID失败:', error.message);

            // 尝试使用wxcode模块获取OpenID
            try {
                console.log('🔄 尝试通过wxcode模块获取OpenID...');
                const wxcode = require('./wxcode');
                const result = await wxcode.getOpenid(this.wxid, this.appId);

                if (result.success) {
                    this.userInfo.openId = result.openid;
                    console.log(`✅ OpenID (通过wxcode): ${this.userInfo.openId}`);
                    return { openId: result.openid };
                }
            } catch (wxcodeError) {
                console.log(`⚠️ wxcode模块获取OpenID也失败: ${wxcodeError.message}`);
            }

            throw error;
        }
    }

    // 4. 静默登录Token获取
    async fetchSilentLoginToken(code) {
        console.log('\n🔐 步骤4: 静默登录Token获取...');
        
        const url = `${NIKE_CONFIG.accounts.url}/bind_user_tokens/wechat_mp/v1`;
        const data = {
            appId: this.appId,
            code: code
        };

        try {
            const response = await httpRequest('POST', url, data, {
                'HOST': 'accounts.nike.com.cn',
                'withoutToken': 'true'
            });
            
            if (response.data && response.data.data) {
                this.tokens.silentToken = response.data.data;
                console.log('✅ 静默登录Token获取成功');
                return response.data.data;
            } else {
                throw new Error('静默登录Token响应格式错误');
            }
        } catch (error) {
            console.error('❌ 静默登录Token获取失败:', error.message);
            throw error;
        }
    }

    // 5. OAuth2.0 Token获取
    async fetchOAuthToken(authCode) {
        console.log('\n🔐 步骤5: OAuth2.0 Token获取...');
        
        const pkce = generatePKCE();
        const url = `${NIKE_CONFIG.accounts.url}/token/v1`;
        const data = {
            redirect_uri: NIKE_CONFIG.accounts.redirectUri,
            grant_type: 'authorization_code',
            code: authCode,
            client_id: NIKE_CONFIG.accounts.clientId,
            code_verifier: pkce.codeVerifier
        };

        try {
            const response = await httpRequest('POST', url, data, {
                'Content-Type': 'application/x-www-form-urlencoded',
                'HOST': 'accounts.nike.com.cn',
                'withoutToken': 'true'
            });
            
            if (response.data) {
                this.tokens.accessToken = response.data.access_token;
                this.tokens.refreshToken = response.data.refresh_token;
                console.log('✅ OAuth2.0 Token获取成功');
                return response.data;
            } else {
                throw new Error('OAuth2.0 Token响应格式错误');
            }
        } catch (error) {
            console.error('❌ OAuth2.0 Token获取失败:', error.message);
            throw error;
        }
    }

    // 6. 测试已认证的API调用
    async testAuthenticatedAPI() {
        console.log('\n🔐 步骤6: 测试已认证的API调用...');

        if (!this.tokens.accessToken && !this.tokens.wechatToken) {
            console.log('⚠️ 没有可用的访问令牌，跳过API测试');
            return;
        }

        const authToken = this.tokens.accessToken || this.tokens.wechatToken;

        // 测试多个Nike API端点
        const testAPIs = [
            {
                name: '菜单分类',
                url: `${NIKE_CONFIG.wechatApi}/onemp/menu_categories/v1`,
                method: 'GET'
            },
            {
                name: '子菜单分类',
                url: `${NIKE_CONFIG.wechatApi}/onemp/sub_menu_categories/v1`,
                method: 'POST',
                data: { id: 5193 }
            },
            {
                name: '分类分享信息',
                url: `${NIKE_CONFIG.wechatApi}/onemp/category/sharing/v1`,
                method: 'GET'
            }
        ];

        for (const api of testAPIs) {
            try {
                console.log(`\n🧪 测试API: ${api.name}`);
                const response = await httpRequest(api.method, api.url, api.data || null, {
                    'Authorization': `Bearer ${authToken}`,
                    'App-Id': `wechat:mp:${this.appId}`,
                    'XWeb_XHR': '1'
                });

                console.log(`✅ ${api.name} API调用成功`);
                if (enableLogs) {
                    console.log('📊 响应数据:', JSON.stringify(response.data, null, 2));
                }
            } catch (error) {
                console.error(`❌ ${api.name} API调用失败:`, error.message);
            }
        }
    }

    // 7. 获取用户详细信息
    async getUserProfile() {
        console.log('\n🔐 步骤7: 获取用户详细信息...');

        if (!this.userInfo.openId) {
            console.log('⚠️ 缺少OpenID，跳过用户信息获取');
            return;
        }

        try {
            // 尝试使用wxcode模块获取用户信息
            const wxcode = require('./wxcode');
            const cloudFunctionData = JSON.stringify({
                "api_name": "webapi_getuserprofile",
                "data": {
                    "app_version": 68,
                    "desc": "用于获取您的个人信息",
                    "lang": "zh_CN",
                    "version": "3.7.12"
                },
                "env": 1,
                "operate_directly": false,
                "show_confirm": true,
                "tid": Date.now() * 1000000 + Math.floor(Math.random() * 1000000),
                "with_credentials": true
            });

            const result = await wxcode.getUserInfo(this.wxid, this.appId, cloudFunctionData);
            if (result.success) {
                this.userInfo.profile = result;
                console.log('✅ 用户详细信息获取成功');
                if (enableLogs) {
                    console.log('👤 用户信息:', JSON.stringify(result, null, 2));
                }
            } else {
                throw new Error(result.error || '获取用户信息失败');
            }
        } catch (error) {
            console.log(`⚠️ 获取用户详细信息失败: ${error.message}`);
        }
    }

    // 8. 获取手机号信息
    async getMobileInfo() {
        console.log('\n🔐 步骤8: 获取手机号信息...');

        try {
            const wxcode = require('./wxcode');
            const result = await wxcode.getmobile(this.wxid, this.appId);

            if (result.success) {
                this.userInfo.mobile = result;
                console.log('✅ 手机号信息获取成功');
                if (enableLogs) {
                    console.log('📱 手机号信息:', JSON.stringify(result, null, 2));
                }
            } else {
                throw new Error(result.error || '获取手机号失败');
            }
        } catch (error) {
            console.log(`⚠️ 获取手机号信息失败: ${error.message}`);
        }
    }

    // 执行完整登录流程
    async performFullLogin() {
        console.log(`\n🚀 开始Nike小程序完整登录流程 - 账号: ${this.wxid}`);
        console.log('='.repeat(60));

        try {
            // 步骤1: 获取微信授权码
            const wechatCode = await this.getWeChatCode();

            // 步骤2: Nike微信API登录
            await this.nikeWeChatAPILogin(wechatCode);

            // 步骤3: 获取OpenID和UnionID
            await this.getWeChatOpenId(wechatCode);

            // 步骤4: 静默登录Token获取
            await this.fetchSilentLoginToken(wechatCode);

            // 步骤5: OAuth2.0 Token获取 (可选，需要授权码)
            // await this.fetchOAuthToken(authCode);

            // 步骤6: 测试已认证的API调用
            await this.testAuthenticatedAPI();

            // 步骤7: 获取用户详细信息
            await this.getUserProfile();

            // 步骤8: 获取手机号信息
            await this.getMobileInfo();

            console.log('\n🎉 Nike小程序登录流程完成！');
            console.log('='.repeat(60));
            console.log('📊 登录结果汇总:');
            console.log('👤 用户信息:', JSON.stringify(this.userInfo, null, 2));
            console.log('🔑 令牌类型:', Object.keys(this.tokens));

            // 生成测试报告
            this.generateTestReport();

        } catch (error) {
            console.error(`\n❌ Nike小程序登录流程失败: ${error.message}`);
            if (enableLogs) {
                console.error('详细错误信息:', error);
            }
            throw error;
        }
    }

    // 生成测试报告
    generateTestReport() {
        console.log('\n📋 Nike小程序登录测试报告');
        console.log('='.repeat(60));

        const report = {
            测试时间: new Date().toLocaleString('zh-CN'),
            测试账号: this.wxid,
            小程序ID: this.appId,
            登录状态: '成功',
            获得的信息: {
                OpenID: this.userInfo.openId || '未获取',
                UnionID: this.userInfo.unionId || '未获取',
                用户资料: this.userInfo.profile ? '已获取' : '未获取',
                手机号: this.userInfo.mobile ? '已获取' : '未获取'
            },
            获得的令牌: {
                微信Token: this.tokens.wechatToken ? '已获取' : '未获取',
                静默登录Token: this.tokens.silentToken ? '已获取' : '未获取',
                访问Token: this.tokens.accessToken ? '已获取' : '未获取',
                刷新Token: this.tokens.refreshToken ? '已获取' : '未获取'
            },
            API测试: '已完成'
        };

        console.log(JSON.stringify(report, null, 2));

        // 保存报告到文件
        const fs = require('fs');
        const reportFile = `nike-login-report-${Date.now()}.json`;
        try {
            fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
            console.log(`\n💾 测试报告已保存到: ${reportFile}`);
        } catch (error) {
            console.log(`⚠️ 保存测试报告失败: ${error.message}`);
        }
    }
}

// 显示使用说明
function showUsage() {
    console.log(`
🔧 Nike小程序登录测试工具使用说明
=================================

📋 功能说明:
   本工具用于测试Nike小程序的完整登录流程，包括：
   - 微信小程序授权登录
   - Nike API认证
   - OpenID/UnionID获取
   - 用户信息获取
   - API接口测试

📝 使用方法:
   node nike-login-test.js [选项]

🔧 命令行选项:
   --wxid <微信ID>     指定测试用的微信ID
   --help             显示此帮助信息
   --debug            开启详细调试日志

🌍 环境变量:
   TEST_WXID          测试用的微信ID
   NIKE_LOGS          设置为'1'开启详细日志

📚 使用示例:
   node nike-login-test.js --wxid your_wxid_here
   node nike-login-test.js --wxid your_wxid_here --debug
   TEST_WXID=your_wxid_here node nike-login-test.js

⚠️  注意事项:
   - 需要先配置wxcode.js模块以获取真实的微信授权码
   - 某些API需要真实的Nike账户才能正常工作
   - 测试报告会自动保存到当前目录

🔗 相关文件:
   - wxcode.js: 微信授权码获取模块
   - nike-login-report-*.json: 测试报告文件
`);
}

// 主函数
async function main() {
    const args = process.argv.slice(2);

    // 检查是否需要显示帮助
    if (args.includes('--help') || args.includes('-h')) {
        showUsage();
        return;
    }

    console.log('🔔 Nike小程序登录测试开始');
    console.log('='.repeat(50));

    // 从命令行参数或环境变量获取wxid
    const getArg = (name) => {
        const index = args.indexOf(`--${name}`);
        return index !== -1 && args[index + 1] ? args[index + 1] : null;
    };

    const wxid = getArg('wxid') || process.env.TEST_WXID;
    const debugMode = args.includes('--debug');

    if (!wxid) {
        console.error('❌ 错误: 未指定测试账号');
        console.log('💡 请使用 --wxid 参数指定微信ID，或设置 TEST_WXID 环境变量');
        console.log('💡 使用 --help 查看详细使用说明');
        process.exit(1);
    }

    console.log(`📱 测试账号: ${wxid}`);
    console.log(`🔧 调试模式: ${debugMode || enableLogs ? '开启' : '关闭'}`);
    console.log(`⏰ 开始时间: ${new Date().toLocaleString('zh-CN')}`);
    console.log('='.repeat(50));

    try {
        const nikeLogin = new NikeLogin(wxid);
        await nikeLogin.performFullLogin();

        console.log('\n✅ 测试完成！');
        console.log(`⏰ 结束时间: ${new Date().toLocaleString('zh-CN')}`);
    } catch (error) {
        console.error('\n❌ 测试失败:', error.message);
        if (debugMode || enableLogs) {
            console.error('详细错误信息:', error);
        }
        process.exit(1);
    }
}

// 导出模块
module.exports = {
    NikeLogin,
    NIKE_CONFIG,
    httpRequest
};

// 如果直接运行此文件
if (require.main === module) {
    main().catch(console.error);
}
