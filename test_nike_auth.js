/**
 * Nike认证测试脚本
 */
const wxcode = require('./wxcode');

const APPID = 'wx096c43d1829a7788';
const wxid = 'wxid_ltkystdcspc822';

async function testNikeAuth() {
    console.log('🔔 开始Nike认证测试');
    
    try {
        // 1. 获取微信授权码
        console.log('🔑 获取微信授权码...');
        const codeResult = await wxcode.getWxCode(wxid, APPID);
        if (!codeResult.success) {
            console.log(`❌ 获取授权码失败: ${codeResult.error}`);
            return;
        }
        
        const wxCode = codeResult.code;
        console.log(`✅ 授权码获取成功: ${wxCode}`);
        
        // 2. 测试GC Token接口
        console.log('🔑 测试GC Token接口...');
        const gcLoginData = {
            appId: `wechat:mp:${APPID}`,
            code: wxCode
        };
        
        const gcResponse = await wxcode.httpRequest('POST', 
            'https://wechat.nike.com.cn/wechat_auth/token/v1', 
            gcLoginData, {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        );
        
        console.log('GC Token响应:', gcResponse);
        
        // 3. 获取新的code测试Auth Token接口
        console.log('🔄 获取新的授权码...');
        const newCodeResult = await wxcode.getWxCode(wxid, APPID);
        if (!newCodeResult.success) {
            console.log(`❌ 获取新授权码失败: ${newCodeResult.error}`);
            return;
        }
        
        const newWxCode = newCodeResult.code;
        console.log(`✅ 新授权码获取成功: ${newWxCode}`);
        
        // 4. 测试Auth Token接口
        console.log('🔑 测试Auth Token接口...');
        const authLoginData = {
            appId: APPID,  // 不加前缀
            code: newWxCode
        };
        
        const authResponse = await wxcode.httpRequest('POST', 
            'https://accounts.nike.com.cn/bind_user_tokens/wechat_mp/v1', 
            authLoginData, {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'HOST': 'accounts.nike.com.cn'
            }
        );
        
        console.log('Auth Token响应:', authResponse);
        
        // 5. 如果获取到GC Token，测试签到接口
        if (gcResponse && (gcResponse.accessToken || (gcResponse.data && gcResponse.data.accessToken))) {
            const gcToken = gcResponse.accessToken || gcResponse.data.accessToken;
            console.log('🎯 测试签到接口...');
            
            const redeemResponse = await wxcode.httpRequest('GET',
                'https://wechat.nike.com.cn/onemp/redeem/redeem_center_info/v2',
                null, {
                    'Authorization': `Bearer ${gcToken}`,
                    'Accept': 'application/json'
                }
            );
            
            console.log('签到接口响应:', redeemResponse);
        }
        
    } catch (error) {
        console.log(`❌ 测试失败: ${error.message}`);
        console.error(error);
    }
}

// 运行测试
testNikeAuth().then(() => {
    console.log('🎉 测试完成');
}).catch(error => {
    console.error('❌ 测试异常:', error);
});
